{"doc_name": "earthmover.pdf", "structure": [{"title": "Earth Mover’s Distance based Similarity Search at Scale", "start_index": 1, "end_index": 1, "node_id": "0000"}, {"title": "ABSTRACT", "start_index": 1, "end_index": 1, "node_id": "0001"}, {"title": "INTRODUCTION", "start_index": 1, "end_index": 2, "node_id": "0002"}, {"title": "PRELIMINARIES", "start_index": 2, "end_index": 2, "nodes": [{"title": "Computing the EMD", "start_index": 3, "end_index": 3, "node_id": "0004"}, {"title": "Filter-and-Refinement Framework", "start_index": 3, "end_index": 4, "node_id": "0005"}], "node_id": "0003"}, {"title": "SCALING UP SSP", "start_index": 4, "end_index": 5, "node_id": "0006"}, {"title": "BOOSTING THE REFINEMENT PHASE", "start_index": 5, "end_index": 5, "nodes": [{"title": "Analysis of EMD Calculation", "start_index": 5, "end_index": 6, "node_id": "0008"}, {"title": "Progressive Bounding", "start_index": 6, "end_index": 6, "node_id": "0009"}, {"title": "Sensitivity to Refinement Order", "start_index": 6, "end_index": 7, "node_id": "0010"}, {"title": "Dynamic Refinement Ordering", "start_index": 7, "end_index": 8, "node_id": "0011"}, {"title": "Running Upper Bound", "start_index": 8, "end_index": 8, "node_id": "0012"}], "node_id": "0007"}, {"title": "EXPERIMENTAL EVALUATION", "start_index": 8, "end_index": 9, "nodes": [{"title": "Performance Improvement", "start_index": 9, "end_index": 10, "node_id": "0014"}, {"title": "Scalability Experiments", "start_index": 10, "end_index": 11, "node_id": "0015"}, {"title": "Parameter Tuning in DRO", "start_index": 11, "end_index": 12, "node_id": "0016"}], "node_id": "0013"}, {"title": "RELATED WORK", "start_index": 12, "end_index": 12, "node_id": "0017"}, {"title": "CONCLUSION", "start_index": 12, "end_index": 12, "node_id": "0018"}, {"title": "ACKNOWLEDGMENT", "start_index": 12, "end_index": 12, "node_id": "0019"}, {"title": "REFERENCES", "start_index": 12, "end_index": 12, "node_id": "0020"}]}