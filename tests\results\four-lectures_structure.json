{"doc_name": "four-lectures.pdf", "structure": [{"title": "Preface", "start_index": 1, "end_index": 1, "node_id": "0000"}, {"title": "ML at a Glance", "start_index": 2, "end_index": 2, "nodes": [{"title": "An ML session", "start_index": 2, "end_index": 3, "node_id": "0002"}, {"title": "Types and Values", "start_index": 3, "end_index": 4, "node_id": "0003"}, {"title": "Recursive Functions", "start_index": 4, "end_index": 4, "node_id": "0004"}, {"title": "Raising Exceptions", "start_index": 4, "end_index": 5, "node_id": "0005"}, {"title": "Structures", "start_index": 5, "end_index": 6, "node_id": "0006"}, {"title": "Signatures", "start_index": 6, "end_index": 7, "node_id": "0007"}, {"title": "Coercive Signature Matching", "start_index": 7, "end_index": 8, "node_id": "0008"}, {"title": "Functor Declaration", "start_index": 8, "end_index": 9, "node_id": "0009"}, {"title": "Functor Application", "start_index": 9, "end_index": 9, "node_id": "0010"}, {"title": "Summary", "start_index": 9, "end_index": 9, "node_id": "0011"}], "node_id": "0001"}, {"title": "Programming with ML Modules", "start_index": 10, "end_index": 10, "nodes": [{"title": "Introduction", "start_index": 10, "end_index": 11, "node_id": "0013"}, {"title": "Signatures", "start_index": 11, "end_index": 12, "node_id": "0014"}, {"title": "Structures", "start_index": 12, "end_index": 13, "node_id": "0015"}, {"title": "Functors", "start_index": 13, "end_index": 14, "node_id": "0016"}, {"title": "Substructures", "start_index": 14, "end_index": 15, "node_id": "0017"}, {"title": "Sharing", "start_index": 15, "end_index": 16, "node_id": "0018"}, {"title": "Building the System", "start_index": 16, "end_index": 17, "node_id": "0019"}, {"title": "Separate Compilation", "start_index": 17, "end_index": 18, "node_id": "0020"}, {"title": "Good Style", "start_index": 18, "end_index": 18, "node_id": "0021"}, {"title": "Bad Style", "start_index": 18, "end_index": 19, "node_id": "0022"}], "node_id": "0012"}, {"title": "The Static Semantics of Modules", "start_index": 20, "end_index": 20, "nodes": [{"title": "Elaboration", "start_index": 20, "end_index": 21, "node_id": "0024"}, {"title": "Names", "start_index": 21, "end_index": 21, "node_id": "0025"}, {"title": "Decorating Structures", "start_index": 21, "end_index": 21, "node_id": "0026"}, {"title": "Decorating Signatures", "start_index": 22, "end_index": 23, "node_id": "0027"}, {"title": "Signature Instantiation", "start_index": 23, "end_index": 24, "node_id": "0028"}, {"title": "Signature Matching", "start_index": 24, "end_index": 25, "node_id": "0029"}, {"title": "Signature Constraints", "start_index": 25, "end_index": 25, "node_id": "0030"}, {"title": "Decorating Functors", "start_index": 26, "end_index": 26, "node_id": "0031"}, {"title": "External Sharing", "start_index": 26, "end_index": 27, "node_id": "0032"}, {"title": "Functors with Arguments", "start_index": 27, "end_index": 28, "node_id": "0033"}, {"title": "Sharing Between Argument and Result", "start_index": 28, "end_index": 28, "node_id": "0034"}, {"title": "Explicit Result Signatures", "start_index": 28, "end_index": 29, "node_id": "0035"}], "node_id": "0023"}, {"title": "Implementing an Interpreter in ML", "start_index": 30, "end_index": 32, "nodes": [{"title": "Version 1: The Bare Typechecker", "start_index": 32, "end_index": 33, "node_id": "0037"}, {"title": "Version 2: Adding Lists and Polymorphism", "start_index": 33, "end_index": 37, "node_id": "0038"}, {"title": "Version 3: A Different Implementation of Types", "start_index": 37, "end_index": 39, "node_id": "0039"}, {"title": "Version 4: Introducing Variables and Let", "start_index": 39, "end_index": 43, "node_id": "0040"}, {"title": "Acknowledgement", "start_index": 43, "end_index": 43, "node_id": "0041"}], "node_id": "0036"}, {"title": "Appendix A: The Bare Interpreter", "start_index": 44, "end_index": 44, "nodes": [{"title": "Syntax", "start_index": 44, "end_index": 44, "node_id": "0043"}, {"title": "Parsing", "start_index": 44, "end_index": 45, "node_id": "0044"}, {"title": "Environments", "start_index": 45, "end_index": 45, "node_id": "0045"}, {"title": "Evaluation", "start_index": 45, "end_index": 46, "node_id": "0046"}, {"title": "Type Checking", "start_index": 46, "end_index": 46, "node_id": "0047"}, {"title": "The Interpreter", "start_index": 46, "end_index": 47, "node_id": "0048"}, {"title": "The Evaluator", "start_index": 47, "end_index": 48, "node_id": "0049"}, {"title": "The Typechecker", "start_index": 48, "end_index": 49, "node_id": "0050"}, {"title": "The Basics", "start_index": 50, "end_index": 52, "node_id": "0051"}], "node_id": "0042"}, {"title": "Appendix B: Files", "start_index": 53, "end_index": 53, "node_id": "0052"}]}