

## Document Search Examples


PageIndex currently enables reasoning-based RAG within a single document by default.
For users who need to search across multiple documents, we provide three best-practice workflows for different scenarios below.

* [**Search by Metadata**:](metadata.md) for documents that can be distinguished by metadata.
* [**Search by Semantics**:](semantics.md) for documents with different semantic content or cover diverse topics.
* [**Search by Description**:](description.md) a lightweight strategy for a small number of documents.


## 💬 Support

* 🤝 [Join our Discord](https://discord.gg/VuXuf29EUj)
* 📨 [Contact Us](https://ii2abc2jejf.typeform.com/to/meB40zV0)